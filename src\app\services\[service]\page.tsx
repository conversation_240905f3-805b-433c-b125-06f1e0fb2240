import React from 'react';
import CaseStudyCard from '@components/CaseStudyCard';
import CTA from '@components/CTA';
import Faq from '@components/Faq';
import Insights from '@components/Insights';
import HeroSection from '@components/HeroSection';
import OtherServicesCard from '@components/OtherServicesCard';
import ServicesCard from '@components/ServicesCard';
import Testimonial from '@components/Testimonial';
import TrustedPartners from '@components/TrustedPartners';
import seoSchema from '@utils/seoSchema';
import { notFound } from 'next/navigation';
import ContactUsForm from '@components/ContactUsForm';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import RichResults from '@components/RichResults';

export async function generateStaticParams() {
  const l2Response = await fetchFromStrapi('l2-service-pages');
  const service = l2Response.data.map(res => ({
    service: res.attributes.slug,
  }));

  return service;
}

export async function fetchL2ServiceData(slug: string) {
  const query = `filters[slug][$eq]=${slug}&populate=cta&populate=cta_other&populate=ServiceOfferingCard,ServiceOfferingCard.L2ServicesCard,
    ServiceOfferingCard.L2ServicesCard.on_hover_bg_image&populate=other_services.other_services_card&populate=other_services.all_services_card,other_services.other_services_card.on_hover_bg_image&populate=faq.faq_items,hero_section.image&populate=insights,insights.circular_text_image,insights.blogs.heroSection_image&populate=testimonials,testimonials.testimonials_slider,testimonials.testimonials_slider.image,testimonials_slider.testimonial_video_link,testimonials.testimonial_playbtn_logo,testimonials.circular_text_line_svg&populate=case_study_cards.case_study_relation.preview.preview_background_image&populate=case_study_cards.case_study_relation.hero_section.global_services,seo.schema`;
  return await fetchFromStrapi('l2-service-pages', query);
}

async function getTrustedPartnersData() {
  const query = `populate=trustedPartner.title&populate=trustedPartner.partnersLogo.images`;
  return await fetchFromStrapi('trusted-partner', query);
}

async function getTestimonialsData() {
  const query = `populate=testimonials,testimonials.testimonials_slider,testimonials.testimonials_slider.image,testimonials_slider.testimonial_video_link,testimonials.testimonial_playbtn_logo,testimonials.circular_text_line_svg`;
  return await fetchFromStrapi('testimonial', query);
}

async function getFormData() {
  const query = `populate=form.formFields&populate=form.button`;
  return await fetchFromStrapi('form', query);
}

export async function generateMetadata({
  params,
}: {
  params: { service: string };
}) {
  const { service: slug } = params;

  const query = `filters[slug][$eq]=${slug}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema`;
  const seoFetchedData = await fetchFromStrapi('l2-service-pages', query);

  const seoData = seoFetchedData?.data[0]?.attributes?.seo;
  return seoSchema(seoData);
}

export default async function Services({
  params,
}: {
  params: { service: string };
}) {
  const { service } = params;
  const L2data = await fetchL2ServiceData(service);
  const trustedPartnersData = await getTrustedPartnersData();
  const formData = await getFormData();
  const testimonials = await getTestimonialsData();

  // Check if L2 service page data exists, otherwise return 404
  if (!L2data?.data || L2data?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {L2data?.data[0]?.attributes?.seo && (
        <RichResults data={L2data?.data[0]?.attributes?.seo} />
      )}
      {L2data?.data[0]?.attributes?.hero_section && (
        <HeroSection
          heroData={L2data?.data[0]?.attributes?.hero_section}
          variant="primary"
          L2pageName={L2data?.data[0]?.attributes?.pageName}
          L2pageSlug={L2data?.data[0]?.attributes?.slug}
        />
      )}
      {trustedPartnersData?.data?.attributes?.trustedPartner && (
        <TrustedPartners
          data={trustedPartnersData?.data?.attributes?.trustedPartner}
        />
      )}
      {L2data?.data[0]?.attributes?.ServiceOfferingCard && (
        <ServicesCard
          variant="blackSlideCard"
          l2ServiceData={L2data?.data[0]?.attributes?.ServiceOfferingCard}
          shouldRedirectToL3
        />
      )}
      {L2data?.data[0]?.attributes?.cta && (
        <CTA
          data={L2data?.data[0]?.attributes?.cta}
          variant="scrollToContactForm"
        />
      )}
      {L2data?.data[0]?.attributes?.case_study_cards && (
        <CaseStudyCard
          case_study={L2data?.data[0]?.attributes?.case_study_cards}
          variantWhite
        />
      )}
      {L2data?.data[0]?.attributes?.insights && (
        <Insights data={L2data?.data[0]?.attributes?.insights} />
      )}
      {L2data?.data[0]?.attributes?.cta_other && (
        <CTA
          data={L2data?.data[0]?.attributes?.cta_other}
          variant="scrollToContactForm"
        />
      )}
      {testimonials?.data?.attributes?.testimonials && (
        <Testimonial data={testimonials?.data?.attributes?.testimonials} />
      )}
      {L2data?.data[0]?.attributes?.other_services && (
        <OtherServicesCard
          data={{ ...L2data?.data[0]?.attributes?.other_services }}
          variant="L2Services"
        />
      )}
      {L2data?.data[0]?.attributes?.faq && (
        <Faq faqData={L2data?.data[0]?.attributes?.faq} />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="L2Services"
        />
      )}
    </>
  );
}
