import { NextRequest, NextResponse } from 'next/server';
import sendDataToHubspot from 'common/sendDataToHubSpot';
import sendDataToSendGrid from 'common/sendDataToSendGrid';
import sendToSlack from 'common/sendDataToSlack';
import currentTimestamp from 'common/currentTimestamp';
import { sendCloudMigrationSlackNotification } from '@utils/cloudMigrationSlackNotification';

export async function POST(request: NextRequest) {
  try {
    const form_data = await request.json();

    // Validate required fields
    if (!form_data?.emailAddress || !form_data?.firstName) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 },
      );
    }

    // Prepare form fields for HubSpot
    const formFields = [
      {
        name: 'firstname',
        value: form_data?.firstName ?? '',
      },
      {
        name: 'lastname',
        value: form_data?.lastName ?? '',
      },
      {
        name: 'email',
        value: form_data?.emailAddress ?? '',
      },
      {
        name: 'phone',
        value: form_data?.phoneNumber ?? '',
      },
      {
        name: 'company',
        value: form_data?.companyName ?? '',
      },
      {
        name: 'message',
        value: form_data?.howCanWeHelpYou ?? '',
      },
      {
        name: 'utm_campaign',
        value: form_data?.utm_campaign ?? '',
      },
      {
        name: 'utm_medium',
        value: form_data?.utm_medium ?? '',
      },
      {
        name: 'utm_source',
        value: form_data?.utm_source ?? '',
      },
      {
        name: 'ip_address',
        value: form_data?.ip_address ?? '',
      },
      {
        name: 'ga_4_userid',
        value: form_data?.ga_4_userid ?? '',
      },
      {
        name: 'city',
        value: form_data?.city ?? '',
      },
      {
        name: 'country',
        value: form_data?.country ?? '',
      },
      {
        name: 'secondary_source',
        value: form_data?.secondary_source ?? '',
      },
      {
        name: 'clarity',
        value: form_data?.clarity ?? '',
      },
      {
        name: 'referrer',
        value: form_data?.referrer ?? '',
      },
      // Cloud migration specific fields
      {
        name: 'migration_assessment_data',
        value: form_data?.migration_assessment_data ?? '',
      },
      {
        name: 'total_migration_cost',
        value: form_data?.total_migration_cost ?? '',
      },
      {
        name: 'monthly_operational_cost',
        value: form_data?.monthly_operational_cost ?? '',
      },
      {
        name: 'migration_timeframe',
        value: form_data?.migration_timeframe ?? '',
      },
      {
        name: 'recommended_provider',
        value: form_data?.recommended_provider ?? '',
      },
      {
        name: 'cost_breakdown',
        value: form_data?.cost_breakdown ?? '',
      },
    ];

    const payload = {
      fields: formFields,
      context: { pageUri: form_data?.url },
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_HUBSPOT_API_KEY}`,
      },
    };

    try {
      // Send Data to HubSpot
      const hubspotResponse = await sendDataToHubspot(
        form_data?.secondary_source,
        payload,
        process.env.NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID,
      );

      if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {
        // Send Data to SendGrid if HubSpot submission is successful
        const emailRes = await sendDataToSendGrid(
          process.env.NEXT_PUBLIC_MAIL_TO,
          process.env.NEXT_PUBLIC_MAIL_FROM,
          form_data?.emailAddress,
          process.env.NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_FORM_TEMPLATE_ID,
          form_data,
        );

        // Send enhanced Slack notification for success
        await sendCloudMigrationSlackNotification(
          form_data,
          process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL,
          true
        );

        // NECESSARY LOGS -> FOR DEBUGGING PURPOSE
        console.log(currentTimestamp());
        console.log('Cloud Migration Lead Data', form_data);
        console.log('HubSpot Response', hubspotResponse);
        console.log('SendGrid Email Response', emailRes);

        return NextResponse.json(
          {
            message: 'Form submitted successfully.',
            hubspotResponse: hubspotResponse.message,
          },
          { status: 200 },
        );
      } else {
        console.error('HubSpot submission failed:', hubspotResponse);

        // Send error notification to Slack
        await sendCloudMigrationSlackNotification(
          {
            ...form_data,
            error: 'HubSpot submission failed',
            hubspotResponse: hubspotResponse,
          },
          process.env.NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL,
          false
        );

        return NextResponse.json(
          {
            message: 'Failed to submit form to HubSpot',
            error: hubspotResponse,
          },
          { status: 500 },
        );
      }
    } catch (hubspotError) {
      console.error('HubSpot API Error:', hubspotError);

      // Send error notification to Slack
      await sendToSlack(
        {
          ...form_data,
          error: 'HubSpot API Error',
          errorDetails: hubspotError,
        },
        process.env.NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL,
      );

      return NextResponse.json(
        {
          message: 'Internal server error during HubSpot submission',
          error: hubspotError,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error('Cloud Migration API Error:', error);

    // Send error notification to Slack
    await sendToSlack(
      {
        error: 'Cloud Migration API Error',
        errorDetails: error,
        timestamp: currentTimestamp(),
      },
      process.env.NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL,
    );

    return NextResponse.json(
      {
        message: 'Internal server error',
        error: error,
      },
      { status: 500 },
    );
  }
}
