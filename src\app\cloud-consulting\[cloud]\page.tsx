import React from 'react';
import seoSchema from '@utils/seoSchema';
import HeroSection from '@components/HeroSection';
import CTA from '@components/CTA';
import RichResults from '@components/RichResults';

import { notFound } from 'next/navigation';
import ContactUsForm from '@components/ContactUsForm';
import Testimonial from '@components/Testimonial';
import AuditMethodology from '@components/AuditMethodology';
import PodcastsSeries from '@components/PodcastsSeries';
import Deliverables from '@components/Deliverables';
import TabChallenges from '@components/TabChallenges';
import TitleDescription from '@components/TitleDescription';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import Faq from '@components/Faq';
import TechStack from '@components/TechStack';
import WhyChooseMTL from '@components/WhyChooseMTL';
export async function generateStaticParams() {
  const response = await fetchFromStrapi('cloud-consultings');
  const cloud = response.data.map(res => ({
    cloud: res.attributes.slug,
  }));
  return cloud;
}

export async function fetchCloudConsultingData(slug: string) {
  const queryString = `filters[slug][$eq]=${slug}&populate=hero_section.image,solution,solution_with_video.podcast_episode.video_thumbnail_image,cta,challenges.box.image,our_audit_methodology.box,deliverables.box,seo.schema,scope_and_deliverables.box,our_audit_methodology.box,audit_button,why_choose_mtl.whyChooseMtlCards,tech_stack.tab.logo_url,faq.faq_items`;
  return await fetchFromStrapi('cloud-consultings', queryString);
}

export async function generateMetadata({
  params,
}: {
  params: { cloud: string };
}) {
  const queryString = `filters[slug][$eq]=${params.cloud}&populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords&populate=seo.schema`;
  const seoFetchedData = await fetchFromStrapi(
    'cloud-consultings',
    queryString,
  );
  const seoData = seoFetchedData?.data?.[0]?.attributes?.seo;
  return seoSchema(seoData);
}

async function getFormData() {
  return await fetchFromStrapi(
    'form',
    'populate=form.formFields&populate=form.button',
  );
}

async function getTestimonialsData() {
  return await fetchFromStrapi(
    'testimonial',
    'populate=testimonials,testimonials.testimonials_slider,testimonials.testimonials_slider.image,testimonials_slider.testimonial_video_link,testimonials.testimonial_playbtn_logo,testimonials.circular_text_line_svg',
  );
}
export default async function CloudConsulting({ params }: { params: any }) {
  const { cloud } = params;

  const cloudConsultingData = await fetchCloudConsultingData(cloud);
  const formData = await getFormData();
  const testimonials = await getTestimonialsData();

  // Check if solution page data exists, otherwise return 404
  if (!cloudConsultingData?.data || cloudConsultingData?.data.length === 0) {
    notFound();
  }

  return (
    <>
      {cloudConsultingData?.data[0]?.attributes?.seo && (
        <RichResults data={cloudConsultingData?.data[0]?.attributes?.seo} />
      )}
      {cloudConsultingData?.data[0]?.attributes?.hero_section && (
        <HeroSection
          heroData={cloudConsultingData?.data[0]?.attributes?.hero_section}
          variant="primary"
          cloud_page={true}
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.challenges && (
        <TabChallenges
          tabChallengesData={
            cloudConsultingData?.data[0]?.attributes.challenges
          }
          variant="cloud"
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.scope_and_deliverables && (
        <Deliverables
          datadeliverables={
            cloudConsultingData?.data[0]?.attributes?.scope_and_deliverables
          }
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.solution && (
        <TitleDescription
          dataTitleDescription={
            cloudConsultingData?.data[0]?.attributes?.solution
          }
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.solution_with_video && (
        <PodcastsSeries
          CloudPagePodcastData={
            cloudConsultingData?.data[0]?.attributes?.solution_with_video
          }
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.cta && (
        <CTA
          data={cloudConsultingData?.data[0]?.attributes?.cta}
          variant="scrollToContactForm"
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.our_audit_methodology && (
        <AuditMethodology
          data={cloudConsultingData?.data[0]?.attributes?.our_audit_methodology}
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.deliverables && (
        <Deliverables
          datadeliverables={
            cloudConsultingData?.data[0]?.attributes?.deliverables
          }
        />
      )}
      {testimonials?.data?.attributes?.testimonials && (
        <Testimonial data={testimonials?.data?.attributes?.testimonials} />
      )}
      {cloudConsultingData?.data[0]?.attributes?.why_choose_mtl && (
        <WhyChooseMTL
          data={cloudConsultingData?.data[0]?.attributes?.why_choose_mtl}
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.tech_stack && (
        <TechStack
          data={cloudConsultingData?.data[0]?.attributes?.tech_stack}
        />
      )}
      {cloudConsultingData?.data[0]?.attributes?.faq && (
        <Faq faqData={cloudConsultingData?.data[0]?.attributes?.faq} />
      )}
      {formData?.data?.attributes?.form && (
        <ContactUsForm
          formData={formData?.data?.attributes?.form}
          source="CloudConsulting"
        />
      )}
    </>
  );
}
