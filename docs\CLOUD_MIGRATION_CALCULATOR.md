# Cloud Migration Cost Calculator

## Overview

The Cloud Migration Cost Calculator is a comprehensive assessment tool that helps businesses estimate the costs and timeframe for migrating their infrastructure to the cloud. It follows the same architectural patterns as the AI-readiness feature and provides detailed cost breakdowns, provider comparisons, and personalized recommendations.

## Features

- **Multi-step Assessment**: 5 comprehensive sections covering business setup, workload analysis, cloud preferences, security requirements, and optimization strategies
- **Real-time Cost Calculation**: Dynamic cost estimation based on user inputs using industry-standard algorithms
- **Provider Comparison**: Side-by-side comparison of AWS, Azure, and Google Cloud costs and features
- **Interactive Results**: Visual cost breakdown charts and detailed analysis
- **Lead Capture**: Integrated form for capturing qualified leads with assessment data
- **External Integrations**: HubSpot, SendGrid, and Slack integrations for marketing automation

## Architecture

### Frontend Components

```
src/components/
├── CloudMigrationBody/           # Main container component
├── CloudMigrationQuestions/      # Question rendering and input handling
├── CloudMigrationStep/           # Progress indicator
├── CostBreakdownChart/          # Results visualization
├── CloudMigrationForm/          # Lead capture form
└── __tests__/                   # Component tests
```

### Backend Services

```
src/app/api/cloud-migration/     # API endpoint for form submission
src/utils/cloudMigrationCalculator.ts  # Cost calculation logic
src/utils/cloudMigrationSlackNotification.ts  # Enhanced Slack notifications
```

### Page Structure

```
src/app/cloud-migration-cost-calculator/page.tsx  # Main page component
```

## Installation and Setup

### 1. Dependencies

All required dependencies are already included in the project. The calculator uses:

- React 18+ for UI components
- Next.js 14+ for SSR and API routes
- CSS Modules for styling
- Vitest for testing

### 2. Environment Variables

Add the following environment variables to your `.env.local`:

```env
# HubSpot Configuration
NEXT_PUBLIC_HUBSPOT_API_KEY=your-hubspot-api-key
NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID=your-form-guid

# SendGrid Configuration
NEXT_PUBLIC_SENDGRID_API_KEY=your-sendgrid-api-key
NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_FORM_TEMPLATE_ID=your-template-id
NEXT_PUBLIC_MAIL_TO=<EMAIL>
NEXT_PUBLIC_MAIL_FROM=<EMAIL>

# Slack Configuration
NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL=your-success-webhook-url
NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL=your-error-webhook-url

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### 3. Strapi Content Setup

Follow the detailed Strapi setup instructions in the main implementation to create:

- `cloud-migration-cost-calculator` collection type
- `cloud-migration-question` collection type
- Required components and fields
- Sample assessment data

## Usage

### Accessing the Calculator

The calculator is available at `/cloud-migration-cost-calculator` and follows this user flow:

1. **Hero Section**: Introduction and overview
2. **Assessment Questions**: 5 sections of detailed questions
3. **Cost Calculation**: Real-time processing of inputs
4. **Results Display**: Visual breakdown and provider comparison
5. **Lead Capture**: Form submission with assessment data
6. **Thank You**: Confirmation and next steps

### Assessment Sections

1. **Business & Infrastructure Setup**
   - Migration scope and server count
   - Current infrastructure type
   - Data migration requirements

2. **Workload & Resource Analysis**
   - Workload types and complexity
   - Resource utilization patterns
   - Performance requirements

3. **Cloud Provider & Deployment Preferences**
   - Provider preferences
   - Deployment models
   - Pricing preferences

4. **Security, Compliance & Migration Strategy**
   - Security requirements
   - Compliance needs
   - Migration approach

5. **Post-Migration & Optimization**
   - Monitoring requirements
   - Optimization strategies
   - Support needs

## Cost Calculation Logic

### Base Calculations

The calculator uses industry-standard formulas:

```typescript
// Base server migration cost
const baseServerCost = numberOfServers * 5000

// Infrastructure cost with complexity multipliers
const infrastructureCost = baseServerCost * capacityMultiplier * strategyMultiplier

// Migration services cost
const migrationCost = numberOfServers * 2000 * complexityMultiplier

// Training cost based on team size
const trainingCost = estimatedTeamSize * 2500

// Ongoing support (15% of infrastructure cost)
const supportCost = infrastructureCost * 0.15
```

### Provider Multipliers

- **AWS**: 1.0 (baseline)
- **Azure**: 1.1 (10% premium)
- **Google Cloud**: 1.05 (5% premium)

### Strategy Multipliers

- **Lift-and-shift**: 1.0
- **Replatform**: 1.3
- **Refactor**: 1.8
- **Rebuild**: 2.5

## API Endpoints

### POST /api/cloud-migration

Handles form submission and integrations.

**Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string",
  "emailAddress": "string",
  "phoneNumber": "string",
  "companyName": "string",
  "howCanWeHelpYou": "string",
  "migration_assessment_data": "string (JSON)",
  "total_migration_cost": "number",
  "monthly_operational_cost": "number",
  "migration_timeframe": "string",
  "recommended_provider": "string",
  "cost_breakdown": "string (JSON)",
  // ... tracking and UTM parameters
}
```

**Response:**
```json
{
  "message": "Form submitted successfully.",
  "hubspotResponse": "Success"
}
```

## Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Test Coverage

The test suite includes:

- **Unit Tests**: Individual component functionality
- **Integration Tests**: Complete user flows
- **API Tests**: Backend endpoint testing
- **Calculation Tests**: Cost algorithm validation

### Test Files

```
src/
├── components/CloudMigrationBody/__tests__/
├── components/CloudMigrationQuestions/__tests__/
├── components/CostBreakdownChart/__tests__/
├── app/api/cloud-migration/__tests__/
└── utils/__tests__/
```

## Deployment

### Build Process

```bash
# Install dependencies
npm install

# Run tests
npm run test:run

# Build application
npm run build

# Start production server
npm start
```

### Deployment Checklist

- [ ] Environment variables configured
- [ ] Strapi content types created
- [ ] Assessment questions populated
- [ ] HubSpot form configured
- [ ] SendGrid template created
- [ ] Slack webhooks set up
- [ ] DNS and SSL configured
- [ ] Analytics tracking verified

## Monitoring and Analytics

### Key Metrics to Track

1. **Conversion Metrics**
   - Assessment completion rate
   - Form submission rate
   - Lead quality scores

2. **Performance Metrics**
   - Page load times
   - API response times
   - Error rates

3. **Business Metrics**
   - Cost estimate accuracy
   - Lead-to-customer conversion
   - Revenue attribution

### Error Monitoring

The calculator includes comprehensive error handling:

- Client-side error boundaries
- API error responses
- Slack error notifications
- Graceful fallbacks

## Maintenance

### Regular Tasks

1. **Content Updates**
   - Review and update cost factors
   - Add new provider options
   - Update calculation algorithms

2. **Performance Optimization**
   - Monitor bundle sizes
   - Optimize images and assets
   - Review API performance

3. **Security Updates**
   - Update dependencies
   - Review API security
   - Audit data handling

### Troubleshooting

Common issues and solutions:

1. **Calculator not loading**: Check Strapi API connection
2. **Form submission failing**: Verify environment variables
3. **Incorrect calculations**: Review input data parsing
4. **Integration errors**: Check external service configurations

## Support

For technical support or questions:

1. Check the test suite for expected behavior
2. Review error logs in Slack notifications
3. Consult the Strapi admin panel for content issues
4. Contact the development team for code-related questions
